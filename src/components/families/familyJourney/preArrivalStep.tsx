import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { AlertCircle } from "lucide-react"

export default function PreArrivalStep() {
  return (
    <div className="space-y-8">
      <Card className="border-l-8 border-l-gray-400 shadow-lg">
        <CardHeader className="pb-6">
          <CardTitle className="flex items-center space-x-3">
            <span className="bg-gray-400 text-white px-4 py-2 rounded-full text-sm font-bold">Step 5</span>
            <span className="text-2xl font-bold">Pre-Arrival</span>
          </CardTitle>
          <CardDescription className="text-lg text-gray-600">
            Before your Au Pair arrives in the Netherlands, several important arrangements must be made.
          </CardDescription>
        </CardHeader>
      </Card>

      <Card className="border-2 border-gray-300 bg-gray-50 shadow-lg">
        <CardContent className="pt-8 pb-8">
          <div className="text-center py-12">
            <AlertCircle className="h-20 w-20 text-gray-400 mx-auto mb-6" />
            <h3 className="text-2xl font-bold text-gray-800 mb-4">Step Not Available</h3>
            <p className="text-gray-600 mb-6 text-lg max-w-md mx-auto">
              This step will become available once you have completed the travel and visa arrangements.
            </p>
            <Badge variant="secondary" className="bg-gray-200 text-gray-700 px-4 py-2 text-lg">
              Pending Previous Steps
            </Badge>
          </div>
        </CardContent>
      </Card>

      <div className="flex justify-between pt-8">
        <Button
          variant="outline"
          size="lg"
          className="border-black text-black hover:bg-black hover:text-white px-8 py-3 text-lg"
        >
          GO BACK TO STEP 4
        </Button>
        <Button size="lg" className="bg-gray-400 text-white px-8 py-3 text-lg" disabled>
          GO TO STEP 6
        </Button>
      </div>
    </div>
  )
}
