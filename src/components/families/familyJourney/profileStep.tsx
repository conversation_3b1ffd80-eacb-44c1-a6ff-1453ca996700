import { <PERSON>, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, Clock, Upload } from 'lucide-react';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';

export default function ProfileStep() {
  return (
    <div className="space-y-8">
      <Card className="border-l-8 border-l-black shadow-lg">
        <CardHeader className="pb-6">
          <CardTitle className="flex items-center space-x-3">
            <span className="bg-black text-white px-4 py-2 rounded-full text-sm font-bold">Step 2</span>
            <span className="text-2xl font-bold">Profile</span>
          </CardTitle>
          <CardDescription className="text-lg text-gray-600">
            Set up your full profile for Au Pair candidates to see who you are and what the routine would be like.
          </CardDescription>
        </CardHeader>
      </Card>

      <Accordion type="single" collapsible defaultValue="item-1" className="space-y-4">
        <AccordionItem value="item-1" className="border-2 border-gray-200 rounded-lg">
          <AccordionTrigger className="text-left px-6 py-4 hover:no-underline">
            <div className="flex items-center space-x-4">
              <CheckCircle className="h-6 w-6 text-black" />
              <span className="text-lg font-semibold">2.1 Dear Au-Pair Letter</span>
              <Badge className="bg-black text-white">Completed</Badge>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6">
            <Card className="border-0 shadow-lg">
              <CardContent className="pt-8">
                <div className="grid md:grid-cols-3 gap-8">
                  <div className="md:col-span-2 space-y-6">
                    <div>
                      <h4 className="font-bold text-black text-xl mb-3">To do:</h4>
                      <p className="text-gray-700 text-lg leading-relaxed">
                        Please write a short letter. The 'Dear Au Pair' letter offers a personal touch that only you, as
                        a host family, can convey to your future au pair.
                      </p>
                    </div>

                    <div>
                      <h4 className="font-bold text-black text-xl mb-3">SUGGESTIONS:</h4>
                      <ul className="list-disc list-inside space-y-2 text-gray-700 text-lg">
                        <li>
                          Short introduction of your family, parents, kid(s) information about character and likes
                        </li>
                        <li>
                          Description of family situation (where you live, house, garden surroundings, au pair room)
                        </li>
                        <li>Why would you like to invite an au pair in your family</li>
                        <li>What do you expect from your future Au Pair?</li>
                      </ul>
                    </div>
                  </div>

                  <div className="bg-black text-white p-8 rounded-lg">
                    <h3 className="text-xl font-bold mb-6">Au-Pair Letter</h3>
                    <Textarea
                      placeholder="Write your letter to future au pairs..."
                      className="min-h-40 bg-white/10 border-white/20 text-white placeholder:text-white/70 text-lg"
                      defaultValue="Dear Au Pair,

We are the Johnson family and we're excited to welcome you into our home! We live in a beautiful suburban area with a large garden where our two children, Emma (6) and Lucas (4), love to play.

We're looking forward to sharing our culture with you while learning about yours. We hope this will be an amazing experience for all of us!

Best regards,
The Johnson Family"
                    />
                    <Button className="w-full mt-6 bg-white text-black hover:bg-gray-100">SUBMIT</Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="item-2" className="border-2 border-gray-200 rounded-lg">
          <AccordionTrigger className="text-left px-6 py-4 hover:no-underline">
            <div className="flex items-center space-x-4">
              <CheckCircle className="h-6 w-6 text-black" />
              <span className="text-lg font-semibold">2.2 Family Pictures</span>
              <Badge className="bg-black text-white">Completed</Badge>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6">
            <Card className="border-0 shadow-lg">
              <CardContent className="pt-8">
                <div className="grid md:grid-cols-3 gap-8">
                  <div className="md:col-span-2 space-y-6">
                    <div>
                      <h4 className="font-bold text-black text-xl mb-3">To-Do:</h4>
                      <p className="text-gray-700 text-lg leading-relaxed">
                        Please upload pictures of both parents and the kids.
                      </p>
                      <p className="text-gray-600 mt-3">
                        Please note: Upload only jpeg's, if you like to make collages then you can use a program like
                        piccolage.
                      </p>
                    </div>
                  </div>

                  <div className="bg-gray-900 text-white p-8 rounded-lg">
                    <h3 className="text-xl font-bold mb-6">Family Pictures</h3>
                    <p className="text-sm mb-6 text-gray-300">Upload maximum 4 files</p>
                    <div className="border-2 border-dashed border-white/30 rounded-lg p-8 text-center">
                      <Upload className="h-10 w-10 mx-auto mb-3 text-white/70" />
                      <p className="text-sm text-white/90">Drag and drop files or click to browse</p>
                    </div>
                    <Button className="w-full mt-6 bg-white text-black hover:bg-gray-100">SUBMIT</Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="item-3" className="border-2 border-gray-200 rounded-lg">
          <AccordionTrigger className="text-left px-6 py-4 hover:no-underline">
            <div className="flex items-center space-x-4">
              <Clock className="h-6 w-6 text-gray-600" />
              <span className="text-lg font-semibold">2.3 Weekly Time Schedule</span>
              <Badge className="bg-gray-600 text-white">In Progress</Badge>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6">
            <Card className="border-0 shadow-lg">
              <CardContent className="pt-8">
                <div className="space-y-6">
                  <div className="bg-gray-50 p-6 rounded-lg border-2 border-gray-200">
                    <h4 className="font-bold text-black text-xl mb-3">LAW:</h4>
                    <p className="text-gray-700 text-lg leading-relaxed">
                      You are legally required to create a weekly schedule for your Au Pair. It must be approved, dated
                      and signed by both Host Parents and the Au Pair before we may send in the visa application.
                    </p>
                  </div>

                  <div>
                    <h4 className="font-bold text-black text-xl mb-3">LEGAL BOUNDARIES:</h4>
                    <ul className="list-disc list-inside space-y-2 text-gray-700 text-lg">
                      <li>Light household chores and childcare only</li>
                      <li>Max: 30 hours/week, 8 hours/day</li>
                      <li>At least 2 days off/week</li>
                      <li>1 full weekend off/month (Fri 20:00 – Sun 23:00)</li>
                      <li>Babysitting (even if kids are asleep) counts as working hours</li>
                    </ul>
                  </div>

                  <div className="bg-gray-100 p-8 rounded-lg border-2 border-gray-200">
                    <h4 className="font-bold text-black text-xl mb-4 text-center">Schedule Builder</h4>
                    <p className="text-gray-600 mb-6 text-center text-lg">
                      Use our interactive schedule builder to create your weekly routine.
                    </p>
                    <Button className="w-full bg-black text-white hover:bg-gray-800">Open Schedule Builder</Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </AccordionContent>
        </AccordionItem>
      </Accordion>

      <div className="flex justify-end pt-8">
        <Button size="lg" className="bg-black text-white hover:bg-gray-800 px-8 py-3 text-lg">
          GO TO STEP 3
        </Button>
      </div>
    </div>
  );
}
